{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\nimport Home from './components/Home';\nimport JobDetail from './components/JobDetail';\nimport ScrollToTop from './components/ScrollToTop';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/job/:slug\",\n          element: /*#__PURE__*/_jsxDEV(JobDetail, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Home", "JobDetail", "ScrollToTop", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\nimport Home from './components/Home';\nimport JobDetail from './components/JobDetail';\nimport ScrollToTop from './components/ScrollToTop';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/job/:slug\" element={<JobDetail />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAO,WAAW;AAClB,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,MAAM;IAAAS,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,eAClBF,OAAA,CAACN,MAAM;QAAAQ,QAAA,gBACLF,OAAA,CAACL,KAAK;UAACS,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEL,OAAA,CAACJ,IAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCT,OAAA,CAACL,KAAK;UAACS,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEL,OAAA,CAACH,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACC,EAAA,GAXQT,GAAG;AAaZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}