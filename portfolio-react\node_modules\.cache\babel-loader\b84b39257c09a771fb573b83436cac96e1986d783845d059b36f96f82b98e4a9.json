{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\JobDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useCallback, useEffect, lazy, Suspense } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\n\n// Lazy load heavy components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectImageSwiper = /*#__PURE__*/lazy(_c = () => import('./ProjectImageSwiper'));\n_c2 = ProjectImageSwiper;\nconst NDANotification = /*#__PURE__*/lazy(_c3 = () => import('./NDANotification'));\n_c4 = NDANotification;\nconst JobDetail = () => {\n  _s();\n  const {\n    slug\n  } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({\n    isOpen: false,\n    projectTitle: ''\n  });\n\n  // Memoized NDA check\n  const isNDAProject = useCallback(project => {\n    return project.description.toLowerCase().includes('nda') || project.title.toLowerCase().includes('nda') || project.images.some(img => img.includes('NDA'));\n  }, []);\n\n  // Memoized handler\n  const handleProjectInfoClick = useCallback((e, project) => {\n    e.stopPropagation();\n    if (isNDAProject(project)) {\n      setNdaNotification({\n        isOpen: true,\n        projectTitle: project.title\n      });\n      return;\n    }\n    window.open(project.liveUrl, '_blank');\n  }, [isNDAProject]);\n  const closeNdaNotification = useCallback(() => {\n    setNdaNotification({\n      isOpen: false,\n      projectTitle: ''\n    });\n  }, []);\n  if (!job) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '100px 20px',\n          textAlign: 'center',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Job Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            color: '#4B0082'\n          },\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"back-navigation\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/#experience\",\n        className: \"back-button\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"back-arrow\",\n          children: \"\\u2190\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Back to Timeline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"job-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"company-branding\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: job.logo,\n            alt: job.logoAlt,\n            className: \"hero-company-logo\",\n            loading: \"lazy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"job-title-hero\",\n              children: job.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"company-name-hero\",\n              children: job.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), job.companyLink && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"company-link-hero\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: job.companyLink,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: job.companyLink\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-duration-hero\",\n              children: job.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"job-summary\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: job.summary\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"job-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Role Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: job.roleOverview\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Key Responsibilities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: job.responsibilities.map((responsibility, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: responsibility\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Technologies & Skills\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skills-grid\",\n            children: Object.entries(job.skills).map(([category, skills]) => {\n              // Generate class name based on category name\n              const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `skill-category ${categoryClass}`,\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"skill-tags\",\n                  children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"skill-tag\",\n                    children: skill\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this)]\n              }, category, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Key Accomplishments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"accomplishments-list\",\n            children: job.accomplishments.map((accomplishment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"accomplishment-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: accomplishment.metric\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-description\",\n                children: accomplishment.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"role-projects\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Projects from this Role\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"projects-grid\",\n        children: job.projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"project-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-image\",\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  minHeight: 200\n                },\n                children: \"Loading images...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ProjectImageSwiper, {\n                images: project.images,\n                title: project.title,\n                isNDA: isNDAProject(project)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-info\",\n            onClick: e => handleProjectInfoClick(e, project),\n            style: {\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-tech\",\n              children: project.technologies.map((tech, techIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tech\n              }, techIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), project.liveUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-link\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: null,\n      children: /*#__PURE__*/_jsxDEV(NDANotification, {\n        isOpen: ndaNotification.isOpen,\n        onClose: closeNdaNotification,\n        projectTitle: ndaNotification.projectTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(JobDetail, \"0yoHoslmJcooGDpbjhDwFJnS/RU=\", false, function () {\n  return [useParams];\n});\n_c5 = JobDetail;\nexport default JobDetail;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ProjectImageSwiper$lazy\");\n$RefreshReg$(_c2, \"ProjectImageSwiper\");\n$RefreshReg$(_c3, \"NDANotification$lazy\");\n$RefreshReg$(_c4, \"NDANotification\");\n$RefreshReg$(_c5, \"JobDetail\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useCallback", "useEffect", "lazy", "Suspense", "useParams", "Link", "jobsData", "Header", "Footer", "jsxDEV", "_jsxDEV", "ProjectImageSwiper", "_c", "_c2", "NDANotification", "_c3", "_c4", "JobDetail", "_s", "slug", "job", "find", "ndaNotification", "setNdaNotification", "isOpen", "projectTitle", "isNDAProject", "project", "description", "toLowerCase", "includes", "title", "images", "some", "img", "handleProjectInfoClick", "e", "stopPropagation", "window", "open", "liveUrl", "closeNdaNotification", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "textAlign", "color", "to", "className", "src", "logo", "alt", "logoAlt", "loading", "company", "companyLink", "href", "target", "rel", "duration", "summary", "roleOverview", "responsibilities", "map", "responsibility", "index", "Object", "entries", "skills", "category", "categoryClass", "replace", "skill", "accomplishments", "accomplishment", "metric", "projects", "fallback", "minHeight", "isNDA", "onClick", "cursor", "technologies", "tech", "techIndex", "onClose", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/JobDetail.js"], "sourcesContent": ["import React, { useState, useMemo, useCallback, useEffect, lazy, Suspense } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\n\n// Lazy load heavy components\nconst ProjectImageSwiper = lazy(() => import('./ProjectImageSwiper'));\nconst NDANotification = lazy(() => import('./NDANotification'));\n\nconst JobDetail = () => {\n  const { slug } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });\n\n  // Memoized NDA check\n  const isNDAProject = useCallback((project) => {\n    return project.description.toLowerCase().includes('nda') ||\n           project.title.toLowerCase().includes('nda') ||\n           project.images.some(img => img.includes('NDA'));\n  }, []);\n\n  // Memoized handler\n  const handleProjectInfoClick = useCallback((e, project) => {\n    e.stopPropagation();\n    if (isNDAProject(project)) {\n      setNdaNotification({ isOpen: true, projectTitle: project.title });\n      return;\n    }\n    window.open(project.liveUrl, '_blank');\n  }, [isNDAProject]);\n\n  const closeNdaNotification = useCallback(() => {\n    setNdaNotification({ isOpen: false, projectTitle: '' });\n  }, []);\n\n  if (!job) {\n    return (\n      <div>\n        <Header />\n        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>\n          <h1>Job Not Found</h1>\n          <Link to=\"/\" style={{ color: '#4B0082' }}>← Back to Home</Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Header />\n      \n      {/* Navigation Back */}\n      <div className=\"back-navigation\">\n        <Link to=\"/#experience\" className=\"back-button\">\n          <span className=\"back-arrow\">←</span>\n          <span>Back to Timeline</span>\n        </Link>\n      </div>\n\n      {/* Job Detail Hero Section */}\n      <section className=\"job-hero\">\n        <div className=\"job-hero-content\">\n          <div className=\"company-branding\">\n            <img \n              src={job.logo} \n              alt={job.logoAlt} \n              className=\"hero-company-logo\" \n              loading=\"lazy\"\n            />\n            <div className=\"company-info\">\n              <h1 className=\"job-title-hero\">{job.title}</h1>\n              <h2 className=\"company-name-hero\">{job.company}</h2>\n              {job.companyLink && (\n                <p className=\"company-link-hero\">\n                  <a href={job.companyLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                    {job.companyLink}\n                  </a>\n                </p>\n              )}\n              <p className=\"job-duration-hero\">{job.duration}</p>\n            </div>\n          </div>\n          <div className=\"job-summary\">\n            <p>{job.summary}</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Job Details Content */}\n      <section className=\"job-content\">\n        <div className=\"content-grid\">\n          {/* Full Job Description */}\n          <div className=\"content-card\">\n            <h3>Role Overview</h3>\n            <p>{job.roleOverview}</p>\n            \n            <h4>Key Responsibilities</h4>\n            <ul>\n              {job.responsibilities.map((responsibility, index) => (\n                <li key={index}>{responsibility}</li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills & Technologies */}\n          <div className=\"content-card\">\n            <h3>Technologies & Skills</h3>\n            <div className=\"skills-grid\">\n              {Object.entries(job.skills).map(([category, skills]) => {\n                // Generate class name based on category name\n                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n                return (\n                  <div key={category} className={`skill-category ${categoryClass}`}>\n                    <h4>{category}</h4>\n                    <div className=\"skill-tags\">\n                      {skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Key Accomplishments */}\n          <div className=\"content-card\">\n            <h3>Key Accomplishments</h3>\n            <div className=\"accomplishments-list\">\n              {job.accomplishments.map((accomplishment, index) => (\n                <div key={index} className=\"accomplishment-item\">\n                  <div className=\"metric\">{accomplishment.metric}</div>\n                  <div className=\"metric-description\">{accomplishment.description}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Project Portfolio from this role */}\n      <section className=\"role-projects\">\n        <h2>Projects from this Role</h2>\n        <div className=\"projects-grid\">\n          {job.projects.map((project, index) => (\n            <div\n              key={index}\n              className=\"project-card\"\n            >\n              <div className=\"project-image\">\n                <Suspense fallback={<div style={{minHeight: 200}}>Loading images...</div>}>\n                  <ProjectImageSwiper\n                    images={project.images}\n                    title={project.title}\n                    isNDA={isNDAProject(project)}\n                  />\n                </Suspense>\n              </div>\n              <div\n                className=\"project-info\"\n                onClick={(e) => handleProjectInfoClick(e, project)}\n                style={{ cursor: 'pointer' }}\n              >\n                <h3>{project.title}</h3>\n                <p>{project.description}</p>\n                <div className=\"project-tech\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span key={techIndex}>{tech}</span>\n                  ))}\n                </div>\n                {project.liveUrl && (\n                  <div className=\"project-link\">\n                    <span>\n                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* NDA Notification Modal */}\n      <Suspense fallback={null}>\n        <NDANotification\n          isOpen={ndaNotification.isOpen}\n          onClose={closeNdaNotification}\n          projectTitle={ndaNotification.projectTitle}\n        />\n      </Suspense>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default JobDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AACxF,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,mBAAmB;AAC1B,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,gBAAGT,IAAI,CAAAU,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,GAAA,GAAhEF,kBAAkB;AACxB,MAAMG,eAAe,gBAAGZ,IAAI,CAAAa,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA1DF,eAAe;AAErB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC5B,MAAMgB,GAAG,GAAGd,QAAQ,CAACe,IAAI,CAACD,GAAG,IAAIA,GAAG,CAACD,IAAI,KAAKA,IAAI,CAAC;EACnD,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC;IAAE0B,MAAM,EAAE,KAAK;IAAEC,YAAY,EAAE;EAAG,CAAC,CAAC;;EAE3F;EACA,MAAMC,YAAY,GAAG1B,WAAW,CAAE2B,OAAO,IAAK;IAC5C,OAAOA,OAAO,CAACC,WAAW,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDH,OAAO,CAACI,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAC3CH,OAAO,CAACK,MAAM,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,sBAAsB,GAAGnC,WAAW,CAAC,CAACoC,CAAC,EAAET,OAAO,KAAK;IACzDS,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAIX,YAAY,CAACC,OAAO,CAAC,EAAE;MACzBJ,kBAAkB,CAAC;QAAEC,MAAM,EAAE,IAAI;QAAEC,YAAY,EAAEE,OAAO,CAACI;MAAM,CAAC,CAAC;MACjE;IACF;IACAO,MAAM,CAACC,IAAI,CAACZ,OAAO,CAACa,OAAO,EAAE,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;EAElB,MAAMe,oBAAoB,GAAGzC,WAAW,CAAC,MAAM;IAC7CuB,kBAAkB,CAAC;MAAEC,MAAM,EAAE,KAAK;MAAEC,YAAY,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACL,GAAG,EAAE;IACR,oBACEV,OAAA;MAAAgC,QAAA,gBACEhC,OAAA,CAACH,MAAM;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVpC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAR,QAAA,gBACzEhC,OAAA;UAAAgC,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBpC,OAAA,CAACL,IAAI;UAAC8C,EAAE,EAAC,GAAG;UAACJ,KAAK,EAAE;YAAEG,KAAK,EAAE;UAAU,CAAE;UAAAR,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNpC,OAAA,CAACF,MAAM;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEpC,OAAA;IAAAgC,QAAA,gBACEhC,OAAA,CAACH,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVpC,OAAA;MAAK0C,SAAS,EAAC,iBAAiB;MAAAV,QAAA,eAC9BhC,OAAA,CAACL,IAAI;QAAC8C,EAAE,EAAC,cAAc;QAACC,SAAS,EAAC,aAAa;QAAAV,QAAA,gBAC7ChC,OAAA;UAAM0C,SAAS,EAAC,YAAY;UAAAV,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrCpC,OAAA;UAAAgC,QAAA,EAAM;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNpC,OAAA;MAAS0C,SAAS,EAAC,UAAU;MAAAV,QAAA,eAC3BhC,OAAA;QAAK0C,SAAS,EAAC,kBAAkB;QAAAV,QAAA,gBAC/BhC,OAAA;UAAK0C,SAAS,EAAC,kBAAkB;UAAAV,QAAA,gBAC/BhC,OAAA;YACE2C,GAAG,EAAEjC,GAAG,CAACkC,IAAK;YACdC,GAAG,EAAEnC,GAAG,CAACoC,OAAQ;YACjBJ,SAAS,EAAC,mBAAmB;YAC7BK,OAAO,EAAC;UAAM;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFpC,OAAA;YAAK0C,SAAS,EAAC,cAAc;YAAAV,QAAA,gBAC3BhC,OAAA;cAAI0C,SAAS,EAAC,gBAAgB;cAAAV,QAAA,EAAEtB,GAAG,CAACW;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/CpC,OAAA;cAAI0C,SAAS,EAAC,mBAAmB;cAAAV,QAAA,EAAEtB,GAAG,CAACsC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACnD1B,GAAG,CAACuC,WAAW,iBACdjD,OAAA;cAAG0C,SAAS,EAAC,mBAAmB;cAAAV,QAAA,eAC9BhC,OAAA;gBAAGkD,IAAI,EAAExC,GAAG,CAACuC,WAAY;gBAACE,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAAApB,QAAA,EAChEtB,GAAG,CAACuC;cAAW;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACJ,eACDpC,OAAA;cAAG0C,SAAS,EAAC,mBAAmB;cAAAV,QAAA,EAAEtB,GAAG,CAAC2C;YAAQ;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpC,OAAA;UAAK0C,SAAS,EAAC,aAAa;UAAAV,QAAA,eAC1BhC,OAAA;YAAAgC,QAAA,EAAItB,GAAG,CAAC4C;UAAO;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpC,OAAA;MAAS0C,SAAS,EAAC,aAAa;MAAAV,QAAA,eAC9BhC,OAAA;QAAK0C,SAAS,EAAC,cAAc;QAAAV,QAAA,gBAE3BhC,OAAA;UAAK0C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3BhC,OAAA;YAAAgC,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBpC,OAAA;YAAAgC,QAAA,EAAItB,GAAG,CAAC6C;UAAY;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBpC,OAAA;YAAAgC,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BpC,OAAA;YAAAgC,QAAA,EACGtB,GAAG,CAAC8C,gBAAgB,CAACC,GAAG,CAAC,CAACC,cAAc,EAAEC,KAAK,kBAC9C3D,OAAA;cAAAgC,QAAA,EAAiB0B;YAAc,GAAtBC,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAsB,CACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNpC,OAAA;UAAK0C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3BhC,OAAA;YAAAgC,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BpC,OAAA;YAAK0C,SAAS,EAAC,aAAa;YAAAV,QAAA,EACzB4B,MAAM,CAACC,OAAO,CAACnD,GAAG,CAACoD,MAAM,CAAC,CAACL,GAAG,CAAC,CAAC,CAACM,QAAQ,EAAED,MAAM,CAAC,KAAK;cACtD;cACA,MAAME,aAAa,GAAGD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC8C,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,SAAS;cACnF,oBACEjE,OAAA;gBAAoB0C,SAAS,EAAE,kBAAkBsB,aAAa,EAAG;gBAAAhC,QAAA,gBAC/DhC,OAAA;kBAAAgC,QAAA,EAAK+B;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBpC,OAAA;kBAAK0C,SAAS,EAAC,YAAY;kBAAAV,QAAA,EACxB8B,MAAM,CAACL,GAAG,CAAC,CAACS,KAAK,EAAEP,KAAK,kBACvB3D,OAAA;oBAAkB0C,SAAS,EAAC,WAAW;oBAAAV,QAAA,EAAEkC;kBAAK,GAAnCP,KAAK;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GANE2B,QAAQ;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOb,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpC,OAAA;UAAK0C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3BhC,OAAA;YAAAgC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BpC,OAAA;YAAK0C,SAAS,EAAC,sBAAsB;YAAAV,QAAA,EAClCtB,GAAG,CAACyD,eAAe,CAACV,GAAG,CAAC,CAACW,cAAc,EAAET,KAAK,kBAC7C3D,OAAA;cAAiB0C,SAAS,EAAC,qBAAqB;cAAAV,QAAA,gBAC9ChC,OAAA;gBAAK0C,SAAS,EAAC,QAAQ;gBAAAV,QAAA,EAAEoC,cAAc,CAACC;cAAM;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDpC,OAAA;gBAAK0C,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAEoC,cAAc,CAAClD;cAAW;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAF9DuB,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpC,OAAA;MAAS0C,SAAS,EAAC,eAAe;MAAAV,QAAA,gBAChChC,OAAA;QAAAgC,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCpC,OAAA;QAAK0C,SAAS,EAAC,eAAe;QAAAV,QAAA,EAC3BtB,GAAG,CAAC4D,QAAQ,CAACb,GAAG,CAAC,CAACxC,OAAO,EAAE0C,KAAK,kBAC/B3D,OAAA;UAEE0C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAExBhC,OAAA;YAAK0C,SAAS,EAAC,eAAe;YAAAV,QAAA,eAC5BhC,OAAA,CAACP,QAAQ;cAAC8E,QAAQ,eAAEvE,OAAA;gBAAKqC,KAAK,EAAE;kBAACmC,SAAS,EAAE;gBAAG,CAAE;gBAAAxC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAE;cAAAJ,QAAA,eACxEhC,OAAA,CAACC,kBAAkB;gBACjBqB,MAAM,EAAEL,OAAO,CAACK,MAAO;gBACvBD,KAAK,EAAEJ,OAAO,CAACI,KAAM;gBACrBoD,KAAK,EAAEzD,YAAY,CAACC,OAAO;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNpC,OAAA;YACE0C,SAAS,EAAC,cAAc;YACxBgC,OAAO,EAAGhD,CAAC,IAAKD,sBAAsB,CAACC,CAAC,EAAET,OAAO,CAAE;YACnDoB,KAAK,EAAE;cAAEsC,MAAM,EAAE;YAAU,CAAE;YAAA3C,QAAA,gBAE7BhC,OAAA;cAAAgC,QAAA,EAAKf,OAAO,CAACI;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBpC,OAAA;cAAAgC,QAAA,EAAIf,OAAO,CAACC;YAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BpC,OAAA;cAAK0C,SAAS,EAAC,cAAc;cAAAV,QAAA,EAC1Bf,OAAO,CAAC2D,YAAY,CAACnB,GAAG,CAAC,CAACoB,IAAI,EAAEC,SAAS,kBACxC9E,OAAA;gBAAAgC,QAAA,EAAuB6C;cAAI,GAAhBC,SAAS;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLnB,OAAO,CAACa,OAAO,iBACd9B,OAAA;cAAK0C,SAAS,EAAC,cAAc;cAAAV,QAAA,eAC3BhC,OAAA;gBAAAgC,QAAA,EACGhB,YAAY,CAACC,OAAO,CAAC,GAAG,0BAA0B,GAAG;cAAyB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA/BDuB,KAAK;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpC,OAAA,CAACP,QAAQ;MAAC8E,QAAQ,EAAE,IAAK;MAAAvC,QAAA,eACvBhC,OAAA,CAACI,eAAe;QACdU,MAAM,EAAEF,eAAe,CAACE,MAAO;QAC/BiE,OAAO,EAAEhD,oBAAqB;QAC9BhB,YAAY,EAAEH,eAAe,CAACG;MAAa;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEXpC,OAAA,CAACF,MAAM;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA3LID,SAAS;EAAA,QACIb,SAAS;AAAA;AAAAsF,GAAA,GADtBzE,SAAS;AA6Lf,eAAeA,SAAS;AAAC,IAAAL,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAA0E,GAAA;AAAAC,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}