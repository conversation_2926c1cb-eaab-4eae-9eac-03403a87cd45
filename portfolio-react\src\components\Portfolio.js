import React, { useEffect, useRef } from 'react';

const Portfolio = () => {
  const carouselTrackRef = useRef(null);

  useEffect(() => {
    const carouselTrack = carouselTrackRef.current;
    if (!carouselTrack) return;

    let isDragging = false;
    let isHovering = false;
    let startX;
    let startScrollLeft;
    let autoScrollInterval;
    let scrollSpeed = 1;
    let isMobile = window.innerWidth <= 768;
    let lastTouchTime = 0;

    // Auto-scroll function
    const startAutoScroll = () => {
      if (autoScrollInterval) clearInterval(autoScrollInterval);
      autoScrollInterval = setInterval(() => {
        if (!isDragging && !isHovering) {
          // Pause auto-scroll on mobile when user might be interacting
          if (isMobile && (Date.now() - lastTouchTime < 3000)) {
            return; // Don't auto-scroll for 3 seconds after last touch
          }

          carouselTrack.scrollLeft += scrollSpeed;
          const trackWidth = carouselTrack.scrollWidth / 2;
          if (carouselTrack.scrollLeft >= trackWidth) {
            carouselTrack.scrollLeft = 0;
          }
        }
      }, 16);
    };

    const stopAutoScroll = () => {
      if (autoScrollInterval) {
        clearInterval(autoScrollInterval);
        autoScrollInterval = null;
      }
    };

    // Mouse events
    const handleMouseEnter = () => {
      isHovering = true;
    };

    const handleMouseLeave = () => {
      isHovering = false;
    };

    const handleMouseDown = (e) => {
      isDragging = true;
      isHovering = true;
      carouselTrack.classList.add('dragging');
      startX = e.pageX;
      startScrollLeft = carouselTrack.scrollLeft;
    };

    const handleMouseMove = (e) => {
      if (!isDragging) return;
      const x = e.pageX;
      const walk = (x - startX) * 1.8;
      carouselTrack.scrollLeft = startScrollLeft - walk;

      const trackWidth = carouselTrack.scrollWidth / 2;
      if (carouselTrack.scrollLeft >= trackWidth) {
        carouselTrack.scrollLeft = 0;
      } else if (carouselTrack.scrollLeft < 0) {
        carouselTrack.scrollLeft = trackWidth - 1;
      }
    };

    const handleMouseUp = () => {
      if (isDragging) {
        isDragging = false;
        carouselTrack.classList.remove('dragging');
        setTimeout(() => {
          if (!isHovering) {
            // Auto-scroll will resume
          }
        }, 100);
      }
    };

    const handleWheel = (e) => {
      e.preventDefault();
      const wheelDelta = e.deltaY;
      const scrollAmount = wheelDelta > 0 ? 50 : -50;
      carouselTrack.scrollLeft += scrollAmount;

      const trackWidth = carouselTrack.scrollWidth / 2;
      if (carouselTrack.scrollLeft >= trackWidth) {
        carouselTrack.scrollLeft = 0;
      } else if (carouselTrack.scrollLeft < 0) {
        carouselTrack.scrollLeft = trackWidth - 1;
      }
    };

    // Touch event handlers
    const handleTouchStart = (e) => {
      lastTouchTime = Date.now();
      isDragging = true;
      isHovering = true;
      carouselTrack.classList.add('dragging');
      startX = e.touches[0].pageX;
      startScrollLeft = carouselTrack.scrollLeft;
    };

    const handleTouchMove = (e) => {
      if (!isDragging) return;
      // For mobile, let the browser handle native scrolling
      if (isMobile) {
        // Just track the movement, let native scrolling work
        return;
      }

      // For tablets and larger screens, use custom scrolling
      const x = e.touches[0].pageX;
      const walk = (x - startX) * 1.8;
      carouselTrack.scrollLeft = startScrollLeft - walk;

      const trackWidth = carouselTrack.scrollWidth / 2;
      if (carouselTrack.scrollLeft >= trackWidth) {
        carouselTrack.scrollLeft = 0;
      } else if (carouselTrack.scrollLeft < 0) {
        carouselTrack.scrollLeft = trackWidth - 1;
      }
    };

    const handleTouchEnd = () => {
      if (isDragging) {
        lastTouchTime = Date.now();
        isDragging = false;
        isHovering = false;
        carouselTrack.classList.remove('dragging');
      }
    };

    // Handle window resize for mobile detection
    const handleResize = () => {
      isMobile = window.innerWidth <= 768;
    };

    // Handle scroll for mobile infinite loop
    const handleScroll = () => {
      if (isMobile) {
        const trackWidth = carouselTrack.scrollWidth / 2;
        if (carouselTrack.scrollLeft >= trackWidth) {
          carouselTrack.scrollLeft = 0;
        } else if (carouselTrack.scrollLeft < 0) {
          carouselTrack.scrollLeft = trackWidth - 1;
        }
      }
    };

    // Add event listeners
    carouselTrack.addEventListener('mouseenter', handleMouseEnter);
    carouselTrack.addEventListener('mouseleave', handleMouseLeave);
    carouselTrack.addEventListener('mousedown', handleMouseDown);
    carouselTrack.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    carouselTrack.addEventListener('wheel', handleWheel, { passive: false });

    // Touch event listeners
    carouselTrack.addEventListener('touchstart', handleTouchStart, { passive: true });
    carouselTrack.addEventListener('touchmove', handleTouchMove, { passive: true });
    carouselTrack.addEventListener('touchend', handleTouchEnd, { passive: true });

    // Additional event listeners
    window.addEventListener('resize', handleResize);
    carouselTrack.addEventListener('scroll', handleScroll);

    // Start auto-scroll
    startAutoScroll();

    // Cleanup
    return () => {
      stopAutoScroll();
      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);
      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);
      carouselTrack.removeEventListener('mousedown', handleMouseDown);
      carouselTrack.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      carouselTrack.removeEventListener('wheel', handleWheel);

      // Remove touch event listeners
      carouselTrack.removeEventListener('touchstart', handleTouchStart);
      carouselTrack.removeEventListener('touchmove', handleTouchMove);
      carouselTrack.removeEventListener('touchend', handleTouchEnd);

      // Remove additional event listeners
      window.removeEventListener('resize', handleResize);
      carouselTrack.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const portfolioItems = [
    {
      href: "https://threed-e-commerce.onrender.com",
      image: "/3D E-Comm.PNG",
      alt: "3D Ecommerce",
      title: "3D Ecommerce (Finish Soon)"
    },
    {
      href: "#",
      image: "/ex1.webp",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex2.png",
      alt: "Nexit Brand Identity",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex3.webp",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex4.1.png",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex5.png",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/bussniss web UI.PNG",
      alt: "Business Web UI",
      title: "Available in git Will be deployed soon."
    }
  ];

  return (
    <section className="portfolio">
      <h2>Top Projects<br /></h2>
      <button className="discover-button" onClick={() => console.log('Discover more clicked')}>DISCOVER MORE</button>
      <div className="portfolio-carousel">
        <div className="carousel-track" ref={carouselTrackRef}>
          {/* Render items twice for infinite scroll */}
          {[...portfolioItems, ...portfolioItems].map((item, index) => (
            <div key={index} className="portfolio-item">
              <a href={item.href} target="_blank" rel="noopener noreferrer">
                <img src={item.image} alt={item.alt} />
                <p>{item.title}</p>
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
