{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Portfolio.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Portfolio = () => {\n  _s();\n  const carouselTrackRef = useRef(null);\n  useEffect(() => {\n    const carouselTrack = carouselTrackRef.current;\n    if (!carouselTrack) return;\n    let isDragging = false;\n    let isHovering = false;\n    let startX;\n    let startScrollLeft;\n    let autoScrollInterval;\n    let scrollSpeed = 1;\n    let isMobile = window.innerWidth <= 768;\n    let lastTouchTime = 0;\n\n    // Auto-scroll function\n    const startAutoScroll = () => {\n      if (autoScrollInterval) clearInterval(autoScrollInterval);\n      autoScrollInterval = setInterval(() => {\n        if (!isDragging && !isHovering) {\n          // Pause auto-scroll on mobile when user might be interacting\n          if (isMobile && Date.now() - lastTouchTime < 3000) {\n            return; // Don't auto-scroll for 3 seconds after last touch\n          }\n          carouselTrack.scrollLeft += scrollSpeed;\n          const trackWidth = carouselTrack.scrollWidth / 2;\n          if (carouselTrack.scrollLeft >= trackWidth) {\n            carouselTrack.scrollLeft = 0;\n          }\n        }\n      }, 16);\n    };\n    const stopAutoScroll = () => {\n      if (autoScrollInterval) {\n        clearInterval(autoScrollInterval);\n        autoScrollInterval = null;\n      }\n    };\n\n    // Mouse events\n    const handleMouseEnter = () => {\n      isHovering = true;\n    };\n    const handleMouseLeave = () => {\n      isHovering = false;\n    };\n    const handleMouseDown = e => {\n      isDragging = true;\n      isHovering = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n    const handleMouseMove = e => {\n      if (!isDragging) return;\n      const x = e.pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n    const handleMouseUp = () => {\n      if (isDragging) {\n        isDragging = false;\n        carouselTrack.classList.remove('dragging');\n        setTimeout(() => {\n          if (!isHovering) {\n            // Auto-scroll will resume\n          }\n        }, 100);\n      }\n    };\n    const handleWheel = e => {\n      e.preventDefault();\n      const wheelDelta = e.deltaY;\n      const scrollAmount = wheelDelta > 0 ? 50 : -50;\n      carouselTrack.scrollLeft += scrollAmount;\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    // Touch event handlers\n    const handleTouchStart = e => {\n      lastTouchTime = Date.now();\n      isDragging = true;\n      isHovering = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.touches[0].pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n    const handleTouchMove = e => {\n      if (!isDragging) return;\n      // For mobile, let the browser handle native scrolling\n      if (isMobile) {\n        // Just track the movement, let native scrolling work\n        return;\n      }\n\n      // For tablets and larger screens, use custom scrolling\n      const x = e.touches[0].pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n    const handleTouchEnd = () => {\n      if (isDragging) {\n        lastTouchTime = Date.now();\n        isDragging = false;\n        isHovering = false;\n        carouselTrack.classList.remove('dragging');\n      }\n    };\n\n    // Handle window resize for mobile detection\n    const handleResize = () => {\n      isMobile = window.innerWidth <= 768;\n    };\n\n    // Handle scroll for mobile infinite loop\n    const handleScroll = () => {\n      if (isMobile) {\n        const trackWidth = carouselTrack.scrollWidth / 2;\n        if (carouselTrack.scrollLeft >= trackWidth) {\n          carouselTrack.scrollLeft = 0;\n        } else if (carouselTrack.scrollLeft < 0) {\n          carouselTrack.scrollLeft = trackWidth - 1;\n        }\n      }\n    };\n\n    // Add event listeners\n    carouselTrack.addEventListener('mouseenter', handleMouseEnter);\n    carouselTrack.addEventListener('mouseleave', handleMouseLeave);\n    carouselTrack.addEventListener('mousedown', handleMouseDown);\n    carouselTrack.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    carouselTrack.addEventListener('wheel', handleWheel, {\n      passive: false\n    });\n\n    // Touch event listeners\n    carouselTrack.addEventListener('touchstart', handleTouchStart, {\n      passive: true\n    });\n    carouselTrack.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    carouselTrack.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n\n    // Additional event listeners\n    window.addEventListener('resize', handleResize);\n    carouselTrack.addEventListener('scroll', handleScroll);\n\n    // Start auto-scroll\n    startAutoScroll();\n\n    // Cleanup\n    return () => {\n      stopAutoScroll();\n      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);\n      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);\n      carouselTrack.removeEventListener('mousedown', handleMouseDown);\n      carouselTrack.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      carouselTrack.removeEventListener('wheel', handleWheel);\n    };\n  }, []);\n  const portfolioItems = [{\n    href: \"https://threed-e-commerce.onrender.com\",\n    image: \"/3D E-Comm.PNG\",\n    alt: \"3D Ecommerce\",\n    title: \"3D Ecommerce (Finish Soon)\"\n  }, {\n    href: \"#\",\n    image: \"/ex1.webp\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex2.png\",\n    alt: \"Nexit Brand Identity\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex3.webp\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex4.1.png\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex5.png\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/bussniss web UI.PNG\",\n    alt: \"Business Web UI\",\n    title: \"Available in git Will be deployed soon.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"portfolio\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: [\"Top Projects\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 23\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"discover-button\",\n      onClick: () => console.log('Discover more clicked'),\n      children: \"DISCOVER MORE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"portfolio-carousel\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"carousel-track\",\n        ref: carouselTrackRef,\n        children: [...portfolioItems, ...portfolioItems].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"portfolio-item\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.image,\n              alt: item.alt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_s(Portfolio, \"szS5YaQSbEP6Gfx9ah0FKeYvPZs=\");\n_c = Portfolio;\nexport default Portfolio;\nvar _c;\n$RefreshReg$(_c, \"Portfolio\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Portfolio", "_s", "carouselTrackRef", "carouselTrack", "current", "isDragging", "isHovering", "startX", "startScrollLeft", "autoScrollInterval", "scrollSpeed", "isMobile", "window", "innerWidth", "lastTouchTime", "startAutoScroll", "clearInterval", "setInterval", "Date", "now", "scrollLeft", "trackWidth", "scrollWidth", "stopAutoScroll", "handleMouseEnter", "handleMouseLeave", "handleMouseDown", "e", "classList", "add", "pageX", "handleMouseMove", "x", "walk", "handleMouseUp", "remove", "setTimeout", "handleWheel", "preventDefault", "wheelDelta", "deltaY", "scrollAmount", "handleTouchStart", "touches", "handleTouchMove", "handleTouchEnd", "handleResize", "handleScroll", "addEventListener", "document", "passive", "removeEventListener", "portfolioItems", "href", "image", "alt", "title", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "console", "log", "ref", "map", "item", "index", "target", "rel", "src", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Portfolio.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst Portfolio = () => {\n  const carouselTrackRef = useRef(null);\n\n  useEffect(() => {\n    const carouselTrack = carouselTrackRef.current;\n    if (!carouselTrack) return;\n\n    let isDragging = false;\n    let isHovering = false;\n    let startX;\n    let startScrollLeft;\n    let autoScrollInterval;\n    let scrollSpeed = 1;\n    let isMobile = window.innerWidth <= 768;\n    let lastTouchTime = 0;\n\n    // Auto-scroll function\n    const startAutoScroll = () => {\n      if (autoScrollInterval) clearInterval(autoScrollInterval);\n      autoScrollInterval = setInterval(() => {\n        if (!isDragging && !isHovering) {\n          // Pause auto-scroll on mobile when user might be interacting\n          if (isMobile && (Date.now() - lastTouchTime < 3000)) {\n            return; // Don't auto-scroll for 3 seconds after last touch\n          }\n\n          carouselTrack.scrollLeft += scrollSpeed;\n          const trackWidth = carouselTrack.scrollWidth / 2;\n          if (carouselTrack.scrollLeft >= trackWidth) {\n            carouselTrack.scrollLeft = 0;\n          }\n        }\n      }, 16);\n    };\n\n    const stopAutoScroll = () => {\n      if (autoScrollInterval) {\n        clearInterval(autoScrollInterval);\n        autoScrollInterval = null;\n      }\n    };\n\n    // Mouse events\n    const handleMouseEnter = () => {\n      isHovering = true;\n    };\n\n    const handleMouseLeave = () => {\n      isHovering = false;\n    };\n\n    const handleMouseDown = (e) => {\n      isDragging = true;\n      isHovering = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n\n    const handleMouseMove = (e) => {\n      if (!isDragging) return;\n      const x = e.pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    const handleMouseUp = () => {\n      if (isDragging) {\n        isDragging = false;\n        carouselTrack.classList.remove('dragging');\n        setTimeout(() => {\n          if (!isHovering) {\n            // Auto-scroll will resume\n          }\n        }, 100);\n      }\n    };\n\n    const handleWheel = (e) => {\n      e.preventDefault();\n      const wheelDelta = e.deltaY;\n      const scrollAmount = wheelDelta > 0 ? 50 : -50;\n      carouselTrack.scrollLeft += scrollAmount;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    // Touch event handlers\n    const handleTouchStart = (e) => {\n      lastTouchTime = Date.now();\n      isDragging = true;\n      isHovering = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.touches[0].pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n\n    const handleTouchMove = (e) => {\n      if (!isDragging) return;\n      // For mobile, let the browser handle native scrolling\n      if (isMobile) {\n        // Just track the movement, let native scrolling work\n        return;\n      }\n\n      // For tablets and larger screens, use custom scrolling\n      const x = e.touches[0].pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    const handleTouchEnd = () => {\n      if (isDragging) {\n        lastTouchTime = Date.now();\n        isDragging = false;\n        isHovering = false;\n        carouselTrack.classList.remove('dragging');\n      }\n    };\n\n    // Handle window resize for mobile detection\n    const handleResize = () => {\n      isMobile = window.innerWidth <= 768;\n    };\n\n    // Handle scroll for mobile infinite loop\n    const handleScroll = () => {\n      if (isMobile) {\n        const trackWidth = carouselTrack.scrollWidth / 2;\n        if (carouselTrack.scrollLeft >= trackWidth) {\n          carouselTrack.scrollLeft = 0;\n        } else if (carouselTrack.scrollLeft < 0) {\n          carouselTrack.scrollLeft = trackWidth - 1;\n        }\n      }\n    };\n\n    // Add event listeners\n    carouselTrack.addEventListener('mouseenter', handleMouseEnter);\n    carouselTrack.addEventListener('mouseleave', handleMouseLeave);\n    carouselTrack.addEventListener('mousedown', handleMouseDown);\n    carouselTrack.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    carouselTrack.addEventListener('wheel', handleWheel, { passive: false });\n\n    // Touch event listeners\n    carouselTrack.addEventListener('touchstart', handleTouchStart, { passive: true });\n    carouselTrack.addEventListener('touchmove', handleTouchMove, { passive: true });\n    carouselTrack.addEventListener('touchend', handleTouchEnd, { passive: true });\n\n    // Additional event listeners\n    window.addEventListener('resize', handleResize);\n    carouselTrack.addEventListener('scroll', handleScroll);\n\n    // Start auto-scroll\n    startAutoScroll();\n\n    // Cleanup\n    return () => {\n      stopAutoScroll();\n      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);\n      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);\n      carouselTrack.removeEventListener('mousedown', handleMouseDown);\n      carouselTrack.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      carouselTrack.removeEventListener('wheel', handleWheel);\n    };\n  }, []);\n\n  const portfolioItems = [\n    {\n      href: \"https://threed-e-commerce.onrender.com\",\n      image: \"/3D E-Comm.PNG\",\n      alt: \"3D Ecommerce\",\n      title: \"3D Ecommerce (Finish Soon)\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex1.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex2.png\",\n      alt: \"Nexit Brand Identity\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex3.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex4.1.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex5.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/bussniss web UI.PNG\",\n      alt: \"Business Web UI\",\n      title: \"Available in git Will be deployed soon.\"\n    }\n  ];\n\n  return (\n    <section className=\"portfolio\">\n      <h2>Top Projects<br /></h2>\n      <button className=\"discover-button\" onClick={() => console.log('Discover more clicked')}>DISCOVER MORE</button>\n      <div className=\"portfolio-carousel\">\n        <div className=\"carousel-track\" ref={carouselTrackRef}>\n          {/* Render items twice for infinite scroll */}\n          {[...portfolioItems, ...portfolioItems].map((item, index) => (\n            <div key={index} className=\"portfolio-item\">\n              <a href={item.href} target=\"_blank\" rel=\"noopener noreferrer\">\n                <img src={item.image} alt={item.alt} />\n                <p>{item.title}</p>\n              </a>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Portfolio;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,gBAAgB,GAAGL,MAAM,CAAC,IAAI,CAAC;EAErCD,SAAS,CAAC,MAAM;IACd,MAAMO,aAAa,GAAGD,gBAAgB,CAACE,OAAO;IAC9C,IAAI,CAACD,aAAa,EAAE;IAEpB,IAAIE,UAAU,GAAG,KAAK;IACtB,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,MAAM;IACV,IAAIC,eAAe;IACnB,IAAIC,kBAAkB;IACtB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;IACvC,IAAIC,aAAa,GAAG,CAAC;;IAErB;IACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIN,kBAAkB,EAAEO,aAAa,CAACP,kBAAkB,CAAC;MACzDA,kBAAkB,GAAGQ,WAAW,CAAC,MAAM;QACrC,IAAI,CAACZ,UAAU,IAAI,CAACC,UAAU,EAAE;UAC9B;UACA,IAAIK,QAAQ,IAAKO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,aAAa,GAAG,IAAK,EAAE;YACnD,OAAO,CAAC;UACV;UAEAX,aAAa,CAACiB,UAAU,IAAIV,WAAW;UACvC,MAAMW,UAAU,GAAGlB,aAAa,CAACmB,WAAW,GAAG,CAAC;UAChD,IAAInB,aAAa,CAACiB,UAAU,IAAIC,UAAU,EAAE;YAC1ClB,aAAa,CAACiB,UAAU,GAAG,CAAC;UAC9B;QACF;MACF,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAId,kBAAkB,EAAE;QACtBO,aAAa,CAACP,kBAAkB,CAAC;QACjCA,kBAAkB,GAAG,IAAI;MAC3B;IACF,CAAC;;IAED;IACA,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;MAC7BlB,UAAU,GAAG,IAAI;IACnB,CAAC;IAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;MAC7BnB,UAAU,GAAG,KAAK;IACpB,CAAC;IAED,MAAMoB,eAAe,GAAIC,CAAC,IAAK;MAC7BtB,UAAU,GAAG,IAAI;MACjBC,UAAU,GAAG,IAAI;MACjBH,aAAa,CAACyB,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvCtB,MAAM,GAAGoB,CAAC,CAACG,KAAK;MAChBtB,eAAe,GAAGL,aAAa,CAACiB,UAAU;IAC5C,CAAC;IAED,MAAMW,eAAe,GAAIJ,CAAC,IAAK;MAC7B,IAAI,CAACtB,UAAU,EAAE;MACjB,MAAM2B,CAAC,GAAGL,CAAC,CAACG,KAAK;MACjB,MAAMG,IAAI,GAAG,CAACD,CAAC,GAAGzB,MAAM,IAAI,GAAG;MAC/BJ,aAAa,CAACiB,UAAU,GAAGZ,eAAe,GAAGyB,IAAI;MAEjD,MAAMZ,UAAU,GAAGlB,aAAa,CAACmB,WAAW,GAAG,CAAC;MAChD,IAAInB,aAAa,CAACiB,UAAU,IAAIC,UAAU,EAAE;QAC1ClB,aAAa,CAACiB,UAAU,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAIjB,aAAa,CAACiB,UAAU,GAAG,CAAC,EAAE;QACvCjB,aAAa,CAACiB,UAAU,GAAGC,UAAU,GAAG,CAAC;MAC3C;IACF,CAAC;IAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI7B,UAAU,EAAE;QACdA,UAAU,GAAG,KAAK;QAClBF,aAAa,CAACyB,SAAS,CAACO,MAAM,CAAC,UAAU,CAAC;QAC1CC,UAAU,CAAC,MAAM;UACf,IAAI,CAAC9B,UAAU,EAAE;YACf;UAAA;QAEJ,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC;IAED,MAAM+B,WAAW,GAAIV,CAAC,IAAK;MACzBA,CAAC,CAACW,cAAc,CAAC,CAAC;MAClB,MAAMC,UAAU,GAAGZ,CAAC,CAACa,MAAM;MAC3B,MAAMC,YAAY,GAAGF,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;MAC9CpC,aAAa,CAACiB,UAAU,IAAIqB,YAAY;MAExC,MAAMpB,UAAU,GAAGlB,aAAa,CAACmB,WAAW,GAAG,CAAC;MAChD,IAAInB,aAAa,CAACiB,UAAU,IAAIC,UAAU,EAAE;QAC1ClB,aAAa,CAACiB,UAAU,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAIjB,aAAa,CAACiB,UAAU,GAAG,CAAC,EAAE;QACvCjB,aAAa,CAACiB,UAAU,GAAGC,UAAU,GAAG,CAAC;MAC3C;IACF,CAAC;;IAED;IACA,MAAMqB,gBAAgB,GAAIf,CAAC,IAAK;MAC9Bb,aAAa,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1Bd,UAAU,GAAG,IAAI;MACjBC,UAAU,GAAG,IAAI;MACjBH,aAAa,CAACyB,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvCtB,MAAM,GAAGoB,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACb,KAAK;MAC3BtB,eAAe,GAAGL,aAAa,CAACiB,UAAU;IAC5C,CAAC;IAED,MAAMwB,eAAe,GAAIjB,CAAC,IAAK;MAC7B,IAAI,CAACtB,UAAU,EAAE;MACjB;MACA,IAAIM,QAAQ,EAAE;QACZ;QACA;MACF;;MAEA;MACA,MAAMqB,CAAC,GAAGL,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACb,KAAK;MAC5B,MAAMG,IAAI,GAAG,CAACD,CAAC,GAAGzB,MAAM,IAAI,GAAG;MAC/BJ,aAAa,CAACiB,UAAU,GAAGZ,eAAe,GAAGyB,IAAI;MAEjD,MAAMZ,UAAU,GAAGlB,aAAa,CAACmB,WAAW,GAAG,CAAC;MAChD,IAAInB,aAAa,CAACiB,UAAU,IAAIC,UAAU,EAAE;QAC1ClB,aAAa,CAACiB,UAAU,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAIjB,aAAa,CAACiB,UAAU,GAAG,CAAC,EAAE;QACvCjB,aAAa,CAACiB,UAAU,GAAGC,UAAU,GAAG,CAAC;MAC3C;IACF,CAAC;IAED,MAAMwB,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIxC,UAAU,EAAE;QACdS,aAAa,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1Bd,UAAU,GAAG,KAAK;QAClBC,UAAU,GAAG,KAAK;QAClBH,aAAa,CAACyB,SAAS,CAACO,MAAM,CAAC,UAAU,CAAC;MAC5C;IACF,CAAC;;IAED;IACA,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzBnC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;IACrC,CAAC;;IAED;IACA,MAAMkC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIpC,QAAQ,EAAE;QACZ,MAAMU,UAAU,GAAGlB,aAAa,CAACmB,WAAW,GAAG,CAAC;QAChD,IAAInB,aAAa,CAACiB,UAAU,IAAIC,UAAU,EAAE;UAC1ClB,aAAa,CAACiB,UAAU,GAAG,CAAC;QAC9B,CAAC,MAAM,IAAIjB,aAAa,CAACiB,UAAU,GAAG,CAAC,EAAE;UACvCjB,aAAa,CAACiB,UAAU,GAAGC,UAAU,GAAG,CAAC;QAC3C;MACF;IACF,CAAC;;IAED;IACAlB,aAAa,CAAC6C,gBAAgB,CAAC,YAAY,EAAExB,gBAAgB,CAAC;IAC9DrB,aAAa,CAAC6C,gBAAgB,CAAC,YAAY,EAAEvB,gBAAgB,CAAC;IAC9DtB,aAAa,CAAC6C,gBAAgB,CAAC,WAAW,EAAEtB,eAAe,CAAC;IAC5DvB,aAAa,CAAC6C,gBAAgB,CAAC,WAAW,EAAEjB,eAAe,CAAC;IAC5DkB,QAAQ,CAACD,gBAAgB,CAAC,SAAS,EAAEd,aAAa,CAAC;IACnD/B,aAAa,CAAC6C,gBAAgB,CAAC,OAAO,EAAEX,WAAW,EAAE;MAAEa,OAAO,EAAE;IAAM,CAAC,CAAC;;IAExE;IACA/C,aAAa,CAAC6C,gBAAgB,CAAC,YAAY,EAAEN,gBAAgB,EAAE;MAAEQ,OAAO,EAAE;IAAK,CAAC,CAAC;IACjF/C,aAAa,CAAC6C,gBAAgB,CAAC,WAAW,EAAEJ,eAAe,EAAE;MAAEM,OAAO,EAAE;IAAK,CAAC,CAAC;IAC/E/C,aAAa,CAAC6C,gBAAgB,CAAC,UAAU,EAAEH,cAAc,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;;IAE7E;IACAtC,MAAM,CAACoC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C3C,aAAa,CAAC6C,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;;IAEtD;IACAhC,eAAe,CAAC,CAAC;;IAEjB;IACA,OAAO,MAAM;MACXQ,cAAc,CAAC,CAAC;MAChBpB,aAAa,CAACgD,mBAAmB,CAAC,YAAY,EAAE3B,gBAAgB,CAAC;MACjErB,aAAa,CAACgD,mBAAmB,CAAC,YAAY,EAAE1B,gBAAgB,CAAC;MACjEtB,aAAa,CAACgD,mBAAmB,CAAC,WAAW,EAAEzB,eAAe,CAAC;MAC/DvB,aAAa,CAACgD,mBAAmB,CAAC,WAAW,EAAEpB,eAAe,CAAC;MAC/DkB,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEjB,aAAa,CAAC;MACtD/B,aAAa,CAACgD,mBAAmB,CAAC,OAAO,EAAEd,WAAW,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,cAAc,GAAG,CACrB;IACEC,IAAI,EAAE,wCAAwC;IAC9CC,KAAK,EAAE,gBAAgB;IACvBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,YAAY;IACnBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,sBAAsB;IAC7BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEzD,OAAA;IAAS0D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAC5B3D,OAAA;MAAA2D,QAAA,GAAI,cAAY,eAAA3D,OAAA;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3B/D,OAAA;MAAQ0D,SAAS,EAAC,iBAAiB;MAACM,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE;MAAAP,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC/G/D,OAAA;MAAK0D,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjC3D,OAAA;QAAK0D,SAAS,EAAC,gBAAgB;QAACS,GAAG,EAAEhE,gBAAiB;QAAAwD,QAAA,EAEnD,CAAC,GAAGN,cAAc,EAAE,GAAGA,cAAc,CAAC,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtDtE,OAAA;UAAiB0D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eACzC3D,OAAA;YAAGsD,IAAI,EAAEe,IAAI,CAACf,IAAK;YAACiB,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAb,QAAA,gBAC3D3D,OAAA;cAAKyE,GAAG,EAAEJ,IAAI,CAACd,KAAM;cAACC,GAAG,EAAEa,IAAI,CAACb;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC/D,OAAA;cAAA2D,QAAA,EAAIU,IAAI,CAACZ;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GAJIO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC7D,EAAA,CA5PID,SAAS;AAAAyE,EAAA,GAATzE,SAAS;AA8Pf,eAAeA,SAAS;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}