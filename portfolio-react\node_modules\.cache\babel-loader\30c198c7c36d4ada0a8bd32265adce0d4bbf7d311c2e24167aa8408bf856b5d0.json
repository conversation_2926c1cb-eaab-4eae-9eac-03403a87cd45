{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Statistics.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Statistics = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"statistics\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"STATISTICS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"9+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"WEBSITES DEVELOPED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"14\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Web DESIGN PROJECTS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"3+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"YEARS OF EXPERIENCE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-image\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/business-8398066.jpg\",\n        alt: \"Business statistics visualization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/contact\",\n      className: \"action-button\",\n      children: \"LET'S TALK\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Statistics;\nexport default Statistics;\nvar _c;\n$RefreshReg$(_c, \"Statistics\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Statistics", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Statistics.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Statistics = () => {\n  return (\n    <section className=\"statistics\">\n      <h2>STATISTICS</h2>\n      <div className=\"stats-grid\">\n        <div className=\"stat\">\n          <h3>9+</h3>\n          <p>WEBSITES DEVELOPED</p>\n        </div>\n        <div className=\"stat\">\n          <h3>14</h3>\n          <p>Web DESIGN PROJECTS</p>\n        </div>\n        <div className=\"stat\">\n          <h3>3+</h3>\n          <p>YEARS OF EXPERIENCE</p>\n        </div>\n      </div>\n      <div className=\"stats-image\">\n        <img src=\"/business-8398066.jpg\" alt=\"Business statistics visualization\" />\n      </div>\n      <Link to=\"/contact\" className=\"action-button\">LET'S TALK</Link>\n    </section>\n  );\n};\n\nexport default Statistics;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAASE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAC7BH,OAAA;MAAAG,QAAA,EAAI;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnBP,OAAA;MAAKE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBH,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBH,OAAA;UAAAG,QAAA,EAAI;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACXP,OAAA;UAAAG,QAAA,EAAG;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBH,OAAA;UAAAG,QAAA,EAAI;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACXP,OAAA;UAAAG,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBH,OAAA;UAAAG,QAAA,EAAI;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACXP,OAAA;UAAAG,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BH,OAAA;QAAKQ,GAAG,EAAC,uBAAuB;QAACC,GAAG,EAAC;MAAmC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,eACNP,OAAA,CAACF,IAAI;MAACY,EAAE,EAAC,UAAU;MAACR,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CAAC;AAEd,CAAC;AAACI,EAAA,GAxBIV,UAAU;AA0BhB,eAAeA,UAAU;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}