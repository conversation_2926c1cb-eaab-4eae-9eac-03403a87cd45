{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nconst ScrollToTop = () => {\n  _s();\n  const {\n    pathname\n  } = useLocation();\n  useEffect(() => {\n    // Scroll to top when route changes\n    window.scrollTo({\n      top: 0,\n      left: 0,\n      behavior: 'smooth'\n    });\n  }, [pathname]);\n  return null; // This component doesn't render anything\n};\n_s(ScrollToTop, \"+8VPq4+XDMjo/kjL3WLkbwU2Amg=\", false, function () {\n  return [useLocation];\n});\n_c = ScrollToTop;\nexport default ScrollToTop;\nvar _c;\n$RefreshReg$(_c, \"ScrollToTop\");", "map": {"version": 3, "names": ["useEffect", "useLocation", "ScrollToTop", "_s", "pathname", "window", "scrollTo", "top", "left", "behavior", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/ScrollToTop.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\n\nconst ScrollToTop = () => {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    // Scroll to top when route changes\n    window.scrollTo({\n      top: 0,\n      left: 0,\n      behavior: 'smooth'\n    });\n  }, [pathname]);\n\n  return null; // This component doesn't render anything\n};\n\nexport default ScrollToTop;\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAS,CAAC,GAAGH,WAAW,CAAC,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACAK,MAAM,CAACC,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,OAAO,IAAI,CAAC,CAAC;AACf,CAAC;AAACD,EAAA,CAbID,WAAW;EAAA,QACMD,WAAW;AAAA;AAAAS,EAAA,GAD5BR,WAAW;AAejB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}