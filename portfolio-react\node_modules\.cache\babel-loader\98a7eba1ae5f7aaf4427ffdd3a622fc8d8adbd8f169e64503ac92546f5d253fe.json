{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\JobDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useCallback, useEffect, lazy, Suspense } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\n\n// Lazy load heavy components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectImageSwiper = /*#__PURE__*/lazy(_c = () => import('./ProjectImageSwiper'));\n_c2 = ProjectImageSwiper;\nconst NDANotification = /*#__PURE__*/lazy(_c3 = () => import('./NDANotification'));\n_c4 = NDANotification;\nconst JobDetail = () => {\n  _s();\n  const {\n    slug\n  } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({\n    isOpen: false,\n    projectTitle: ''\n  });\n\n  // Scroll to top when component mounts or slug changes\n  useEffect(() => {\n    window.scrollTo({\n      top: 0,\n      left: 0,\n      behavior: 'smooth'\n    });\n  }, [slug]); // Re-run when slug changes (different job selected)\n\n  // Memoized NDA check\n  const isNDAProject = useCallback(project => {\n    return project.description.toLowerCase().includes('nda') || project.title.toLowerCase().includes('nda') || project.images.some(img => img.includes('NDA'));\n  }, []);\n\n  // Memoized handler\n  const handleProjectInfoClick = useCallback((e, project) => {\n    e.stopPropagation();\n    if (isNDAProject(project)) {\n      setNdaNotification({\n        isOpen: true,\n        projectTitle: project.title\n      });\n      return;\n    }\n    window.open(project.liveUrl, '_blank');\n  }, [isNDAProject]);\n  const closeNdaNotification = useCallback(() => {\n    setNdaNotification({\n      isOpen: false,\n      projectTitle: ''\n    });\n  }, []);\n  if (!job) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '100px 20px',\n          textAlign: 'center',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Job Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            color: '#4B0082'\n          },\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"back-navigation\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/#experience\",\n        className: \"back-button\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"back-arrow\",\n          children: \"\\u2190\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Back to Timeline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"job-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"company-branding\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: job.logo,\n            alt: job.logoAlt,\n            className: \"hero-company-logo\",\n            loading: \"lazy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"job-title-hero\",\n              children: job.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"company-name-hero\",\n              children: job.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), job.companyLink && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"company-link-hero\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: job.companyLink,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: job.companyLink\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-duration-hero\",\n              children: job.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"job-summary\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: job.summary\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"job-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Role Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: job.roleOverview\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Key Responsibilities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: job.responsibilities.map((responsibility, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: responsibility\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Technologies & Skills\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skills-grid\",\n            children: Object.entries(job.skills).map(([category, skills]) => {\n              // Generate class name based on category name\n              const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `skill-category ${categoryClass}`,\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"skill-tags\",\n                  children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"skill-tag\",\n                    children: skill\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 21\n                }, this)]\n              }, category, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Key Accomplishments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"accomplishments-list\",\n            children: job.accomplishments.map((accomplishment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"accomplishment-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: accomplishment.metric\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-description\",\n                children: accomplishment.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"role-projects\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Projects from this Role\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"projects-grid\",\n        children: job.projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"project-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-image\",\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  minHeight: 200\n                },\n                children: \"Loading images...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ProjectImageSwiper, {\n                images: project.images,\n                title: project.title,\n                isNDA: isNDAProject(project)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-info\",\n            onClick: e => handleProjectInfoClick(e, project),\n            style: {\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-tech\",\n              children: project.technologies.map((tech, techIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tech\n              }, techIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), project.liveUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-link\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: null,\n      children: /*#__PURE__*/_jsxDEV(NDANotification, {\n        isOpen: ndaNotification.isOpen,\n        onClose: closeNdaNotification,\n        projectTitle: ndaNotification.projectTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(JobDetail, \"WaW0E/ifN1HnoBOzKYbbVCgzHaE=\", false, function () {\n  return [useParams];\n});\n_c5 = JobDetail;\nexport default JobDetail;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ProjectImageSwiper$lazy\");\n$RefreshReg$(_c2, \"ProjectImageSwiper\");\n$RefreshReg$(_c3, \"NDANotification$lazy\");\n$RefreshReg$(_c4, \"NDANotification\");\n$RefreshReg$(_c5, \"JobDetail\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useCallback", "useEffect", "lazy", "Suspense", "useParams", "Link", "jobsData", "Header", "Footer", "jsxDEV", "_jsxDEV", "ProjectImageSwiper", "_c", "_c2", "NDANotification", "_c3", "_c4", "JobDetail", "_s", "slug", "job", "find", "ndaNotification", "setNdaNotification", "isOpen", "projectTitle", "window", "scrollTo", "top", "left", "behavior", "isNDAProject", "project", "description", "toLowerCase", "includes", "title", "images", "some", "img", "handleProjectInfoClick", "e", "stopPropagation", "open", "liveUrl", "closeNdaNotification", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "textAlign", "color", "to", "className", "src", "logo", "alt", "logoAlt", "loading", "company", "companyLink", "href", "target", "rel", "duration", "summary", "roleOverview", "responsibilities", "map", "responsibility", "index", "Object", "entries", "skills", "category", "categoryClass", "replace", "skill", "accomplishments", "accomplishment", "metric", "projects", "fallback", "minHeight", "isNDA", "onClick", "cursor", "technologies", "tech", "techIndex", "onClose", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/JobDetail.js"], "sourcesContent": ["import React, { useState, useMemo, useCallback, useEffect, lazy, Suspense } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\n\n// Lazy load heavy components\nconst ProjectImageSwiper = lazy(() => import('./ProjectImageSwiper'));\nconst NDANotification = lazy(() => import('./NDANotification'));\n\nconst JobDetail = () => {\n  const { slug } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });\n\n  // Scroll to top when component mounts or slug changes\n  useEffect(() => {\n    window.scrollTo({\n      top: 0,\n      left: 0,\n      behavior: 'smooth'\n    });\n  }, [slug]); // Re-run when slug changes (different job selected)\n\n  // Memoized NDA check\n  const isNDAProject = useCallback((project) => {\n    return project.description.toLowerCase().includes('nda') ||\n           project.title.toLowerCase().includes('nda') ||\n           project.images.some(img => img.includes('NDA'));\n  }, []);\n\n  // Memoized handler\n  const handleProjectInfoClick = useCallback((e, project) => {\n    e.stopPropagation();\n    if (isNDAProject(project)) {\n      setNdaNotification({ isOpen: true, projectTitle: project.title });\n      return;\n    }\n    window.open(project.liveUrl, '_blank');\n  }, [isNDAProject]);\n\n  const closeNdaNotification = useCallback(() => {\n    setNdaNotification({ isOpen: false, projectTitle: '' });\n  }, []);\n\n  if (!job) {\n    return (\n      <div>\n        <Header />\n        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>\n          <h1>Job Not Found</h1>\n          <Link to=\"/\" style={{ color: '#4B0082' }}>← Back to Home</Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Header />\n      \n      {/* Navigation Back */}\n      <div className=\"back-navigation\">\n        <Link to=\"/#experience\" className=\"back-button\">\n          <span className=\"back-arrow\">←</span>\n          <span>Back to Timeline</span>\n        </Link>\n      </div>\n\n      {/* Job Detail Hero Section */}\n      <section className=\"job-hero\">\n        <div className=\"job-hero-content\">\n          <div className=\"company-branding\">\n            <img \n              src={job.logo} \n              alt={job.logoAlt} \n              className=\"hero-company-logo\" \n              loading=\"lazy\"\n            />\n            <div className=\"company-info\">\n              <h1 className=\"job-title-hero\">{job.title}</h1>\n              <h2 className=\"company-name-hero\">{job.company}</h2>\n              {job.companyLink && (\n                <p className=\"company-link-hero\">\n                  <a href={job.companyLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                    {job.companyLink}\n                  </a>\n                </p>\n              )}\n              <p className=\"job-duration-hero\">{job.duration}</p>\n            </div>\n          </div>\n          <div className=\"job-summary\">\n            <p>{job.summary}</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Job Details Content */}\n      <section className=\"job-content\">\n        <div className=\"content-grid\">\n          {/* Full Job Description */}\n          <div className=\"content-card\">\n            <h3>Role Overview</h3>\n            <p>{job.roleOverview}</p>\n            \n            <h4>Key Responsibilities</h4>\n            <ul>\n              {job.responsibilities.map((responsibility, index) => (\n                <li key={index}>{responsibility}</li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills & Technologies */}\n          <div className=\"content-card\">\n            <h3>Technologies & Skills</h3>\n            <div className=\"skills-grid\">\n              {Object.entries(job.skills).map(([category, skills]) => {\n                // Generate class name based on category name\n                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n                return (\n                  <div key={category} className={`skill-category ${categoryClass}`}>\n                    <h4>{category}</h4>\n                    <div className=\"skill-tags\">\n                      {skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Key Accomplishments */}\n          <div className=\"content-card\">\n            <h3>Key Accomplishments</h3>\n            <div className=\"accomplishments-list\">\n              {job.accomplishments.map((accomplishment, index) => (\n                <div key={index} className=\"accomplishment-item\">\n                  <div className=\"metric\">{accomplishment.metric}</div>\n                  <div className=\"metric-description\">{accomplishment.description}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Project Portfolio from this role */}\n      <section className=\"role-projects\">\n        <h2>Projects from this Role</h2>\n        <div className=\"projects-grid\">\n          {job.projects.map((project, index) => (\n            <div\n              key={index}\n              className=\"project-card\"\n            >\n              <div className=\"project-image\">\n                <Suspense fallback={<div style={{minHeight: 200}}>Loading images...</div>}>\n                  <ProjectImageSwiper\n                    images={project.images}\n                    title={project.title}\n                    isNDA={isNDAProject(project)}\n                  />\n                </Suspense>\n              </div>\n              <div\n                className=\"project-info\"\n                onClick={(e) => handleProjectInfoClick(e, project)}\n                style={{ cursor: 'pointer' }}\n              >\n                <h3>{project.title}</h3>\n                <p>{project.description}</p>\n                <div className=\"project-tech\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span key={techIndex}>{tech}</span>\n                  ))}\n                </div>\n                {project.liveUrl && (\n                  <div className=\"project-link\">\n                    <span>\n                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* NDA Notification Modal */}\n      <Suspense fallback={null}>\n        <NDANotification\n          isOpen={ndaNotification.isOpen}\n          onClose={closeNdaNotification}\n          projectTitle={ndaNotification.projectTitle}\n        />\n      </Suspense>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default JobDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AACxF,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,mBAAmB;AAC1B,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,gBAAGT,IAAI,CAAAU,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,GAAA,GAAhEF,kBAAkB;AACxB,MAAMG,eAAe,gBAAGZ,IAAI,CAAAa,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA1DF,eAAe;AAErB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC5B,MAAMgB,GAAG,GAAGd,QAAQ,CAACe,IAAI,CAACD,GAAG,IAAIA,GAAG,CAACD,IAAI,KAAKA,IAAI,CAAC;EACnD,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC;IAAE0B,MAAM,EAAE,KAAK;IAAEC,YAAY,EAAE;EAAG,CAAC,CAAC;;EAE3F;EACAxB,SAAS,CAAC,MAAM;IACdyB,MAAM,CAACC,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ;EACA,MAAMY,YAAY,GAAG/B,WAAW,CAAEgC,OAAO,IAAK;IAC5C,OAAOA,OAAO,CAACC,WAAW,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDH,OAAO,CAACI,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAC3CH,OAAO,CAACK,MAAM,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,sBAAsB,GAAGxC,WAAW,CAAC,CAACyC,CAAC,EAAET,OAAO,KAAK;IACzDS,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAIX,YAAY,CAACC,OAAO,CAAC,EAAE;MACzBT,kBAAkB,CAAC;QAAEC,MAAM,EAAE,IAAI;QAAEC,YAAY,EAAEO,OAAO,CAACI;MAAM,CAAC,CAAC;MACjE;IACF;IACAV,MAAM,CAACiB,IAAI,CAACX,OAAO,CAACY,OAAO,EAAE,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC;EAElB,MAAMc,oBAAoB,GAAG7C,WAAW,CAAC,MAAM;IAC7CuB,kBAAkB,CAAC;MAAEC,MAAM,EAAE,KAAK;MAAEC,YAAY,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACL,GAAG,EAAE;IACR,oBACEV,OAAA;MAAAoC,QAAA,gBACEpC,OAAA,CAACH,MAAM;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVxC,OAAA;QAAKyC,KAAK,EAAE;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAR,QAAA,gBACzEpC,OAAA;UAAAoC,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBxC,OAAA,CAACL,IAAI;UAACkD,EAAE,EAAC,GAAG;UAACJ,KAAK,EAAE;YAAEG,KAAK,EAAE;UAAU,CAAE;UAAAR,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNxC,OAAA,CAACF,MAAM;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACExC,OAAA;IAAAoC,QAAA,gBACEpC,OAAA,CAACH,MAAM;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVxC,OAAA;MAAK8C,SAAS,EAAC,iBAAiB;MAAAV,QAAA,eAC9BpC,OAAA,CAACL,IAAI;QAACkD,EAAE,EAAC,cAAc;QAACC,SAAS,EAAC,aAAa;QAAAV,QAAA,gBAC7CpC,OAAA;UAAM8C,SAAS,EAAC,YAAY;UAAAV,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrCxC,OAAA;UAAAoC,QAAA,EAAM;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNxC,OAAA;MAAS8C,SAAS,EAAC,UAAU;MAAAV,QAAA,eAC3BpC,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAV,QAAA,gBAC/BpC,OAAA;UAAK8C,SAAS,EAAC,kBAAkB;UAAAV,QAAA,gBAC/BpC,OAAA;YACE+C,GAAG,EAAErC,GAAG,CAACsC,IAAK;YACdC,GAAG,EAAEvC,GAAG,CAACwC,OAAQ;YACjBJ,SAAS,EAAC,mBAAmB;YAC7BK,OAAO,EAAC;UAAM;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFxC,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAV,QAAA,gBAC3BpC,OAAA;cAAI8C,SAAS,EAAC,gBAAgB;cAAAV,QAAA,EAAE1B,GAAG,CAACgB;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/CxC,OAAA;cAAI8C,SAAS,EAAC,mBAAmB;cAAAV,QAAA,EAAE1B,GAAG,CAAC0C;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACnD9B,GAAG,CAAC2C,WAAW,iBACdrD,OAAA;cAAG8C,SAAS,EAAC,mBAAmB;cAAAV,QAAA,eAC9BpC,OAAA;gBAAGsD,IAAI,EAAE5C,GAAG,CAAC2C,WAAY;gBAACE,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAAApB,QAAA,EAChE1B,GAAG,CAAC2C;cAAW;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACJ,eACDxC,OAAA;cAAG8C,SAAS,EAAC,mBAAmB;cAAAV,QAAA,EAAE1B,GAAG,CAAC+C;YAAQ;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAV,QAAA,eAC1BpC,OAAA;YAAAoC,QAAA,EAAI1B,GAAG,CAACgD;UAAO;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxC,OAAA;MAAS8C,SAAS,EAAC,aAAa;MAAAV,QAAA,eAC9BpC,OAAA;QAAK8C,SAAS,EAAC,cAAc;QAAAV,QAAA,gBAE3BpC,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3BpC,OAAA;YAAAoC,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBxC,OAAA;YAAAoC,QAAA,EAAI1B,GAAG,CAACiD;UAAY;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBxC,OAAA;YAAAoC,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BxC,OAAA;YAAAoC,QAAA,EACG1B,GAAG,CAACkD,gBAAgB,CAACC,GAAG,CAAC,CAACC,cAAc,EAAEC,KAAK,kBAC9C/D,OAAA;cAAAoC,QAAA,EAAiB0B;YAAc,GAAtBC,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAsB,CACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNxC,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3BpC,OAAA;YAAAoC,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BxC,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAAV,QAAA,EACzB4B,MAAM,CAACC,OAAO,CAACvD,GAAG,CAACwD,MAAM,CAAC,CAACL,GAAG,CAAC,CAAC,CAACM,QAAQ,EAAED,MAAM,CAAC,KAAK;cACtD;cACA,MAAME,aAAa,GAAGD,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC6C,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,SAAS;cACnF,oBACErE,OAAA;gBAAoB8C,SAAS,EAAE,kBAAkBsB,aAAa,EAAG;gBAAAhC,QAAA,gBAC/DpC,OAAA;kBAAAoC,QAAA,EAAK+B;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBxC,OAAA;kBAAK8C,SAAS,EAAC,YAAY;kBAAAV,QAAA,EACxB8B,MAAM,CAACL,GAAG,CAAC,CAACS,KAAK,EAAEP,KAAK,kBACvB/D,OAAA;oBAAkB8C,SAAS,EAAC,WAAW;oBAAAV,QAAA,EAAEkC;kBAAK,GAAnCP,KAAK;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GANE2B,QAAQ;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOb,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxC,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3BpC,OAAA;YAAAoC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BxC,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAV,QAAA,EAClC1B,GAAG,CAAC6D,eAAe,CAACV,GAAG,CAAC,CAACW,cAAc,EAAET,KAAK,kBAC7C/D,OAAA;cAAiB8C,SAAS,EAAC,qBAAqB;cAAAV,QAAA,gBAC9CpC,OAAA;gBAAK8C,SAAS,EAAC,QAAQ;gBAAAV,QAAA,EAAEoC,cAAc,CAACC;cAAM;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDxC,OAAA;gBAAK8C,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAEoC,cAAc,CAACjD;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAF9DuB,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxC,OAAA;MAAS8C,SAAS,EAAC,eAAe;MAAAV,QAAA,gBAChCpC,OAAA;QAAAoC,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCxC,OAAA;QAAK8C,SAAS,EAAC,eAAe;QAAAV,QAAA,EAC3B1B,GAAG,CAACgE,QAAQ,CAACb,GAAG,CAAC,CAACvC,OAAO,EAAEyC,KAAK,kBAC/B/D,OAAA;UAEE8C,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAExBpC,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAV,QAAA,eAC5BpC,OAAA,CAACP,QAAQ;cAACkF,QAAQ,eAAE3E,OAAA;gBAAKyC,KAAK,EAAE;kBAACmC,SAAS,EAAE;gBAAG,CAAE;gBAAAxC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAE;cAAAJ,QAAA,eACxEpC,OAAA,CAACC,kBAAkB;gBACjB0B,MAAM,EAAEL,OAAO,CAACK,MAAO;gBACvBD,KAAK,EAAEJ,OAAO,CAACI,KAAM;gBACrBmD,KAAK,EAAExD,YAAY,CAACC,OAAO;cAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNxC,OAAA;YACE8C,SAAS,EAAC,cAAc;YACxBgC,OAAO,EAAG/C,CAAC,IAAKD,sBAAsB,CAACC,CAAC,EAAET,OAAO,CAAE;YACnDmB,KAAK,EAAE;cAAEsC,MAAM,EAAE;YAAU,CAAE;YAAA3C,QAAA,gBAE7BpC,OAAA;cAAAoC,QAAA,EAAKd,OAAO,CAACI;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBxC,OAAA;cAAAoC,QAAA,EAAId,OAAO,CAACC;YAAW;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BxC,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAV,QAAA,EAC1Bd,OAAO,CAAC0D,YAAY,CAACnB,GAAG,CAAC,CAACoB,IAAI,EAAEC,SAAS,kBACxClF,OAAA;gBAAAoC,QAAA,EAAuB6C;cAAI,GAAhBC,SAAS;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLlB,OAAO,CAACY,OAAO,iBACdlC,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAV,QAAA,eAC3BpC,OAAA;gBAAAoC,QAAA,EACGf,YAAY,CAACC,OAAO,CAAC,GAAG,0BAA0B,GAAG;cAAyB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA/BDuB,KAAK;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxC,OAAA,CAACP,QAAQ;MAACkF,QAAQ,EAAE,IAAK;MAAAvC,QAAA,eACvBpC,OAAA,CAACI,eAAe;QACdU,MAAM,EAAEF,eAAe,CAACE,MAAO;QAC/BqE,OAAO,EAAEhD,oBAAqB;QAC9BpB,YAAY,EAAEH,eAAe,CAACG;MAAa;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEXxC,OAAA,CAACF,MAAM;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChC,EAAA,CApMID,SAAS;EAAA,QACIb,SAAS;AAAA;AAAA0F,GAAA,GADtB7E,SAAS;AAsMf,eAAeA,SAAS;AAAC,IAAAL,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAA8E,GAAA;AAAAC,YAAA,CAAAnF,EAAA;AAAAmF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}